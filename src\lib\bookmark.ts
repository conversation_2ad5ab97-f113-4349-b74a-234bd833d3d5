import { fetchData, insertData, deleteData } from './supabase';
import { notifications } from '@mantine/notifications';

export interface BookmarkUtilityProps {
  userEmail: string | null | undefined;
}

/**
 * Check if a contact is bookmarked by the current user
 */
export const checkIfBookmarked = async (
  contactName: string,
  userEmail: string | null | undefined
): Promise<boolean> => {
  if (!userEmail) return false;

  try {
    const existingBookmark = await fetchData('bookmark', {
      select: 'id, contact_name, contact_email',
      filter: [
        { column: 'contact_name', value: contactName.toLowerCase() },
        { column: 'contact_email', value: userEmail }
      ],
      single: true
    });

    return !!existingBookmark.data;
  } catch (error) {
    console.error('Error checking bookmark status:', error);
    return false;
  }
};

/**
 * Get bookmark count for a specific contact
 */
export const getBookmarkCount = async (contactName: string): Promise<number> => {
  try {
    const { data, error } = await fetchData('bookmark', {
      select: 'id',
      filter: [{ column: 'contact_name', value: contactName.toLowerCase() }]
    });

    if (error) {
      console.error('Error fetching bookmark count:', error);
      return 0;
    }

    return Array.isArray(data) ? data.length : 0;
  } catch (error) {
    console.error('Error in getBookmarkCount:', error);
    return 0;
  }
};

/**
 * Toggle bookmark status for a contact
 */
export const toggleBookmark = async (
  contactName: string,
  isCurrentlyBookmarked: boolean,
  userEmail: string | null | undefined
): Promise<boolean> => {
  if (!userEmail) {
    notifications.show({
      title: 'Authentication Error',
      message: 'You must be logged in to bookmark contacts',
      color: 'red',
    });
    return isCurrentlyBookmarked;
  }

  try {
    if (isCurrentlyBookmarked) {
      // Remove bookmark - first get the bookmark ID
      const existingBookmark = await fetchData('bookmark', {
        select: 'id, contact_name, contact_email',
        filter: [
          { column: 'contact_name', value: contactName.toLowerCase() },
          { column: 'contact_email', value: userEmail }
        ],
        single: true
      });

      if (existingBookmark.data && (existingBookmark.data as any).id) {
        await deleteData('bookmark', {
          column: 'id',
          value: (existingBookmark.data as any).id
        });
      }
      notifications.show({
        title: 'Success',
        message: 'Bookmark removed successfully',
        color: 'blue',
      });
      return false;
    } else {
      // Create bookmark data
      const bookmarkData = {
        contact_name: contactName.toLowerCase(),
        contact_email: userEmail
      };

      // Insert bookmark
      const insertResult = await insertData('bookmark', bookmarkData);
      console.log('Bookmark created:', insertResult);
      notifications.show({
        title: 'Success',
        message: 'Contact bookmarked successfully',
        color: 'green',
      });
      return true;
    }
  } catch (error) {
    console.error('Bookmark operation failed:', error);
    notifications.show({
      title: 'Error',
      message: 'Failed to manage bookmark',
      color: 'red',
    });
    return isCurrentlyBookmarked;
  }
};

/**
 * Clean up all bookmarks for a non-existent contact
 */
export const cleanupBookmarksForNonExistentContact = async (
  contactName: string,
  showNotifications: boolean = true
): Promise<number> => {
  try {
    // Find all bookmarks for this contact name
    const bookmarks = await fetchData('bookmark', {
      select: 'id, contact_name, contact_email',
      filter: [{ column: 'contact_name', value: contactName.toLowerCase() }]
    });

    if (bookmarks.data && Array.isArray(bookmarks.data) && bookmarks.data.length > 0) {
      // Delete all bookmarks for this contact
      for (const bookmark of bookmarks.data) {
        await deleteData('bookmark', { column: 'id', value: (bookmark as any).id });
      }

      if (showNotifications) {
        // Show notification about cleanup
        notifications.show({
          title: 'Cleanup Complete',
          message: `Removed ${bookmarks.data.length} bookmark(s) for non-existent user: ${contactName}`,
          color: 'orange',
          autoClose: 5000,
        });
      }

      console.log(`Cleaned up ${bookmarks.data.length} bookmarks for non-existent user: ${contactName}`);
      return bookmarks.data.length;
    }

    return 0;
  } catch (error) {
    console.error('Error cleaning up bookmarks:', error);
    if (showNotifications) {
      notifications.show({
        title: 'Cleanup Error',
        message: 'Failed to clean up bookmarks for non-existent user',
        color: 'red',
        autoClose: 5000,
      });
    }
    return 0;
  }
};
