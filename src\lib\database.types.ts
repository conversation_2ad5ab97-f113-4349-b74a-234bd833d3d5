export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      // Define your tables here
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          full_name: string | null
          avatar_url: string | null
          email: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string | null
        }
      }
      // New people table for ODude Name data with individual columns
      contact: {
        Row: {
          timestamp: string
          name: string
          image: string | null
          description: string | null
          uri: string | null
          profile: string | null
          email: string | null
          website: string | null
          phone: string | null
          tg_bot: string | null
          notes: Json | null

          web2: string | null
          web3: string | null
          links: Json | null
          images: Json | null
          social: Json | null
          crypto: Json | null
          extra: Json | null
          profile_email: string | null
          minted: string | null
        }
        Insert: {
          timestamp?: string
          name: string
          image?: string | null
          description?: string | null
          uri?: string | null
          profile?: string | null
          email?: string | null
          website?: string | null
          phone?: string | null
          tg_bot?: string | null
          notes?: Json | null

          web2?: string | null
          web3?: string | null
          links?: Json | null
          images?: Json | null
          social?: Json | null
          crypto?: Json | null
          extra?: Json | null
          profile_email?: string | null
          minted?: string | null
        }
        Update: {
          timestamp?: string
          name?: string
          image?: string | null
          description?: string | null
          uri?: string | null
          profile?: string | null
          email?: string | null
          website?: string | null
          phone?: string | null
          tg_bot?: string | null
          notes?: Json | null

          web2?: string | null
          web3?: string | null
          links?: Json | null
          images?: Json | null
          social?: Json | null
          crypto?: Json | null
          extra?: Json | null
          profile_email?: string | null
          minted?: string | null
        }
      }
      bookmark: {
        Row: {
          id: number;
          created_at: string;
          contact_name: string;
          contact_email: string;
        };
        Insert: {
          id?: number;
          created_at?: string;
          contact_name: string;
          contact_email: string;
        };
        Update: {
          id?: number;
          created_at?: string;
          contact_name?: string;
          contact_email?: string;
        };
      }
      settings: {
        Row: {
          email: string;
          max_contact_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          email: string;
          max_contact_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          email?: string;
          max_contact_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
      }
      // Add more tables as needed
    }
    Views: {
      // Define your views here
    }
    Functions: {
      // Define your functions here
    }
    Enums: {
      // Define your enums here
    }
  }
}
