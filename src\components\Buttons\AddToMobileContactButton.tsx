'use client';

import { But<PERSON> } from "@mantine/core";
import { IconDeviceMobile } from "@tabler/icons-react";
import { notifications } from '@mantine/notifications';
import { getImage, ImageData } from 'src/lib/common';

interface ContactItem {
  name: string;
  description: string | null;
  image: string | null;
  uri: string | null;
  profile: string | null;
  email: string | null;
  website: string | null;
  phone: string | null;
  tg_bot: string | null;
  notes: Record<string, string> | null;
  web2: string | null;
  web3: string | null;
  links: Record<string, string> | null;
  images: ImageData | null;
  social: Record<string, string> | null;
  crypto: Record<string, string> | null;
  minted: string | null;
}

interface AddToMobileContactButtonProps {
  contact: ContactItem | null;
}

import { getSocialData, getCryptoData, getNotesData, getSocialUrl } from 'src/lib/database-utils';

export function AddToMobileContactButton({ contact }: AddToMobileContactButtonProps) {

  // Function to generate vCard data
  const generateVCard = (contact: ContactItem): string => {
    let vcard = 'BEGIN:VCARD\n';
    vcard += 'VERSION:3.0\n';

    // Name fields
    vcard += `FN:${contact.profile || contact.name}\n`;
    vcard += `N:${contact.profile || contact.name};;;;\n`;

    // Contact photo - use getImage(images, 1) for the contact picture
    if (contact.images) {
      const contactImageUrl = getImage(contact.images, 1);
      // Only add photo if it's not the default avatar
      if (contactImageUrl && !contactImageUrl.includes('avatar-2.png')) {
        try {
          // For vCard, we need to encode the image URL as a reference
          vcard += `PHOTO;VALUE=URI:${contactImageUrl}\n`;
        } catch (error) {
          console.log('Could not add photo to vCard:', error);
        }
      }
    }

    // Contact information - handle multiple phone numbers
    if (contact.phone && contact.phone.trim()) {
      const phoneNumbers = contact.phone.split(/[,\s]+/).filter(phone => phone.trim());
      phoneNumbers.forEach((phone, index) => {
        const cleanPhone = phone.trim();
        if (cleanPhone) {
          // Add type for multiple phones (WORK, HOME, CELL, etc.)
          const phoneType = index === 0 ? 'CELL' : index === 1 ? 'HOME' : 'WORK';
          vcard += `TEL;TYPE=${phoneType}:${cleanPhone}\n`;
        }
      });
    }

    if (contact.email && contact.email.trim()) {
      vcard += `EMAIL:${contact.email}\n`;
    }

    if (contact.website && contact.website.trim()) {
      vcard += `URL:${contact.website}\n`;
    }

    // Organization/Title (using description as title if available)
    if (contact.description && contact.description.trim()) {
      vcard += `TITLE:${contact.description}\n`;
    }

    // Social Media Profiles using proper X-SOCIALPROFILE fields
    const socialData = getSocialData(contact);
    Object.entries(socialData)
      .filter(([platform, handle]) => handle && handle.trim())
      .forEach(([platform, handle]) => {
        vcard += `X-SOCIALPROFILE;type=${platform}:${getSocialUrl(platform, handle)}\n`;
      });

    // Additional URLs for custom links
    if (contact.links && Object.entries(contact.links).some(([key, value]) => key.trim() && value.trim())) {
      Object.entries(contact.links)
        .filter(([name, url]) => name.trim() && url.trim())
        .forEach(([name, url]) => {
          const fullUrl = url.startsWith('http') ? url : `https://${url}`;
          vcard += `URL;X-ABLabel=${name}:${fullUrl}\n`;
        });
    }

    // Notes field - handle JSON notes structure
    const notesData = getNotesData(contact);
    if (Object.keys(notesData).length > 0) {
      // Combine all notes into a single string with titles
      const notesText = Object.entries(notesData)
        .filter(([title, content]) => title.trim() && content.trim())
        .map(([title, content]) => `${title}: ${content}`)
        .join('\\n\\n');

      if (notesText) {
        // Properly escape newlines for vCard format - iOS requires \\n for line breaks
        const escapedNotes = notesText
          .replace(/\r\n/g, '\\n')  // Windows line endings
          .replace(/\n/g, '\\n')    // Unix line endings
          .replace(/\r/g, '\\n');   // Mac line endings
        vcard += `NOTE:${escapedNotes}\n`;
      }
    }

    vcard += 'END:VCARD';
    return vcard;
  };

  // Function to detect mobile device
  const isMobile = () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  };

  // Function to detect iOS
  const isIOS = () => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  };

  // Function to detect Android
  const isAndroid = () => {
    return /Android/i.test(navigator.userAgent);
  };

  // Function to download vCard file (fallback method)
  const downloadVCardFile = (contact: ContactItem) => {
    try {
      const vCardData = generateVCard(contact);

      // For Android, try different MIME types that might work better
      let mimeType = 'text/vcard;charset=utf-8';
      if (isAndroid()) {
        // Some Android apps prefer these MIME types
        mimeType = 'text/x-vcard;charset=utf-8';
      }

      const blob = new Blob([vCardData], { type: mimeType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${contact.name || 'contact'}.vcf`;

      // Add additional attributes for better Android compatibility
      if (isAndroid()) {
        link.setAttribute('type', mimeType);
        link.setAttribute('target', '_blank');
      }

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      const message = isAndroid()
        ? 'Contact file downloaded. Open it from your downloads to add to contacts.'
        : 'Contact file downloaded successfully';

      notifications.show({
        title: 'Success',
        message: message,
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to download contact file',
        color: 'red',
      });
    }
  };

  // Function to add contact to mobile
  const addToMobileContact = (contact: ContactItem) => {
    try {
      if (isMobile()) {
        if (isIOS()) {
          // For iOS - use data URL which opens in Contacts app
          const vCardData = generateVCard(contact);
          const dataUrl = `data:text/vcard;charset=utf-8,${encodeURIComponent(vCardData)}`;
          window.open(dataUrl, '_blank');

          notifications.show({
            title: 'Opening Contacts',
            message: 'Contact will open in your Contacts app',
            color: 'blue',
          });
        } else if (isAndroid()) {
          // For Android - use a more compatible approach
          try {
            const vCardData = generateVCard(contact);

            // Try to use the Web Share API if available (modern Android browsers)
            if (navigator.share && navigator.canShare) {
              const blob = new Blob([vCardData], { type: 'text/vcard' });
              const file = new File([blob], `${contact.name || 'contact'}.vcf`, { type: 'text/vcard' });

              if (navigator.canShare({ files: [file] })) {
                navigator.share({
                  title: `Contact: ${contact.profile || contact.name}`,
                  files: [file]
                }).then(() => {
                  notifications.show({
                    title: 'Contact Shared',
                    message: 'Contact shared successfully',
                    color: 'green',
                  });
                }).catch(() => {
                  // Fallback to download if sharing fails
                  downloadVCardFile(contact);
                });
                return;
              }
            }

            // Fallback: Create a data URL and try to open it
            const dataUrl = `data:text/vcard;charset=utf-8,${encodeURIComponent(vCardData)}`;
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = `${contact.name || 'contact'}.vcf`;
            link.setAttribute('type', 'text/vcard');

            // Try to trigger the contact app
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            notifications.show({
              title: 'Contact Ready',
              message: 'Contact file prepared. Check your downloads or contact app.',
              color: 'blue',
            });
          } catch (error) {
            console.error('Android contact handling failed:', error);
            downloadVCardFile(contact);
          }
        } else {
          // Other mobile browsers - download file
          downloadVCardFile(contact);
        }
      } else {
        // Desktop - download file
        downloadVCardFile(contact);
      }
    } catch (error) {
      console.error('Error adding contact:', error);
      // Fallback to download
      downloadVCardFile(contact);
    }
  };

  // Don't render if no contact
  if (!contact) {
    return null;
  }

  return (
    <Button
      leftSection={<IconDeviceMobile size={16} />}
      variant="filled"
      color="blue"
      size="xs"
      onClick={() => addToMobileContact(contact)}
    >
      Add to Mobile Contact
    </Button>
  );
}
