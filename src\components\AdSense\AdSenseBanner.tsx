'use client';

import { useEffect, useRef } from 'react';
import { Box } from '@mantine/core';
import { ENABLE_ADSENSE, ADSENSE_CLIENT_ID } from 'src/lib/config';

interface AdSenseBannerProps {
  slot: string;
  width?: number | string;
  height?: number;
  responsive?: boolean;
  className?: string;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export function AdSenseBanner({
  slot,
  width = '100vw',
  height = 320,
  responsive = true,
  className
}: AdSenseBannerProps) {
  const adRef = useRef<HTMLModElement>(null);
  const isInitialized = useRef(false);

  // Don't render if AdSense is disabled or client ID is not set
  if (!ENABLE_ADSENSE || !ADSENSE_CLIENT_ID) {
    return null;
  }

  // Don't render if slot is not provided
  if (!slot) {
    return null;
  }

  useEffect(() => {
    // Only initialize once per component instance
    if (isInitialized.current || !adRef.current) {
      return;
    }

    try {
      // Check if the ad element already has ads
      const adElement = adRef.current;
      if (adElement.getAttribute('data-adsbygoogle-status')) {
        return; // Already initialized
      }

      // Initialize adsbygoogle array if it doesn't exist
      if (typeof window !== 'undefined') {
        window.adsbygoogle = window.adsbygoogle || [];
        window.adsbygoogle.push({});
        isInitialized.current = true;
      }
    } catch (error) {
      console.error('AdSense error:', error);
    }
  }, [slot]);

  return (
    <Box className={className} style={{ textAlign: 'center', margin: '20px 0' }}>
      <ins
        ref={adRef}
        className="adsbygoogle"
        style={{
          display: 'inline-block',
          width: responsive ? '100vw' : typeof width === 'number' ? `${width}px` : width,
          height: `${height}px`,
        }}
        data-ad-client={ADSENSE_CLIENT_ID}
        data-ad-slot={slot}
        data-auto-format={responsive ? 'rspv' : undefined}
        data-full-width={responsive ? '' : undefined}
      />
      {responsive && <div style={{ overflow: '' }}></div>}
    </Box>
  );
}
