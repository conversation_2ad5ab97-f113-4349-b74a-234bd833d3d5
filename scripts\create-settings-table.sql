-- Create settings table for user-specific configurations
-- This table stores per-user settings like max_contact_limit

-- Create the settings table
CREATE TABLE IF NOT EXISTS settings (
  email TEXT PRIMARY KEY,
  max_contact_limit INTEGER DEFAULT 4,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_settings_email ON settings (email);

-- Add comment for documentation
COMMENT ON TABLE settings IS 'User-specific settings and configurations';
COMMENT ON COLUMN settings.email IS 'User email address (primary key)';
COMMENT ON COLUMN settings.max_contact_limit IS 'Maximum number of contacts allowed for this user';
COMMENT ON COLUMN settings.created_at IS 'When the settings record was created';
COMMENT ON COLUMN settings.updated_at IS 'When the settings record was last updated';

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_settings_updated_at
    BEFORE UPDATE ON settings
    FOR EACH ROW
    EXECUTE FUNCTION update_settings_updated_at();

-- Insert some example data (optional)
-- INSERT INTO settings (email, max_contact_limit) VALUES 
-- ('<EMAIL>', 100),
-- ('<EMAIL>', 20)
-- ON CONFLICT (email) DO NOTHING;
