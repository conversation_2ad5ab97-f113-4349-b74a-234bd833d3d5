'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Text,
  Pagination,
  LoadingOverlay,
  Alert,
  Avatar,
  Group,
  Badge,
  Card,
} from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';
import { ProfileData, fetchProfilesData, formatTimestamp } from '../../lib/admin';

interface ProfilesTableProps {}

export function ProfilesTable({}: ProfilesTableProps) {
  const [profiles, setProfiles] = useState<ProfileData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  const ITEMS_PER_PAGE = 50;

  const fetchData = async (page: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetchProfilesData(page, ITEMS_PER_PAGE);
      
      setProfiles(response.data);
      if (response.pagination) {
        setTotalPages(response.pagination.totalPages);
        setTotal(response.pagination.total);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch profiles');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(currentPage);
  }, [currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  const rows = profiles.map((profile) => (
    <Table.Tr key={profile.id}>
      <Table.Td>
        <Group gap="sm">
          <Avatar src={profile.avatar_url} size={30} radius="xl" />
          <div>
            <Text size="sm" fw={500}>
              {profile.full_name || 'No name'}
            </Text>
            <Text size="xs" c="dimmed">
              {profile.email}
            </Text>
          </div>
        </Group>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{profile.email}</Text>
      </Table.Td>
      <Table.Td>
        <Text size="sm">
          {profile.updated_at ? formatTimestamp(profile.updated_at) : 'Never'}
        </Text>
      </Table.Td>
      <Table.Td>
        <Badge color="blue" variant="light">
          {profile.contact_count}
        </Badge>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <Card withBorder>
      <div style={{ position: 'relative' }}>
        <LoadingOverlay visible={loading} />
        
        <Text size="lg" fw={500} mb="md">
          Profiles ({total} total)
        </Text>

        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Profile</Table.Th>
              <Table.Th>Email</Table.Th>
              <Table.Th>Last Updated</Table.Th>
              <Table.Th>Contacts</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {rows.length > 0 ? (
              rows
            ) : (
              <Table.Tr>
                <Table.Td colSpan={4}>
                  <Text ta="center" c="dimmed">
                    No profiles found
                  </Text>
                </Table.Td>
              </Table.Tr>
            )}
          </Table.Tbody>
        </Table>

        {totalPages > 1 && (
          <Group justify="center" mt="md">
            <Pagination
              value={currentPage}
              onChange={handlePageChange}
              total={totalPages}
              size="sm"
            />
          </Group>
        )}
      </div>
    </Card>
  );
}
