'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import {
  Container,
  Title,
  Text,
  Alert,
  LoadingOverlay,
  SimpleGrid,
  Paper,
  Group,
  ThemeIcon,
  Tabs,
  Stack,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconUsers,
  IconAddressBook,
  IconBookmark,
  IconShield,
} from '@tabler/icons-react';
import { ADMIN_EMAIL } from 'src/lib/config';
import { AdminStats, fetchAdminStats, isAdmin } from 'src/lib/admin';
import { ProfilesTable } from 'src/components/Admin/ProfilesTable';
import { ContactsTable } from 'src/components/Admin/ContactsTable';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}

function StatCard({ title, value, icon, color }: StatCardProps) {
  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="apart">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {value.toLocaleString()}
          </Text>
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'loading') return;

    if (!session?.user?.email) {
      setError('Please sign in to access the admin dashboard');
      setLoading(false);
      return;
    }

    if (!isAdmin(session.user.email)) {
      setError('Access denied. Admin privileges required.');
      setLoading(false);
      return;
    }

    // Fetch admin stats
    const fetchStats = async () => {
      try {
        const statsData = await fetchAdminStats();
        setStats(statsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [session, status]);

  if (status === 'loading' || loading) {
    return (
      <Container size="xl" py="xl">
        <LoadingOverlay visible />
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="xl" py="xl">
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </Container>
    );
  }

  if (!session?.user?.email || !isAdmin(session.user.email)) {
    return (
      <Container size="xl" py="xl">
        <Alert icon={<IconAlertCircle size={16} />} title="Access Denied" color="red">
          You don't have permission to access this page.
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        {/* Header */}
        <Group>
          <ThemeIcon size={40} radius="md" color="red">
            <IconShield size={24} />
          </ThemeIcon>
          <div>
            <Title order={1}>Admin Dashboard</Title>
            <Text c="dimmed">Manage users, contacts, and system statistics</Text>
          </div>
        </Group>

        {/* Statistics Cards */}
        {stats && (
          <SimpleGrid cols={{ base: 1, sm: 3 }} spacing="md">
            <StatCard
              title="Total Profiles"
              value={stats.profiles}
              icon={<IconUsers size={18} />}
              color="blue"
            />
            <StatCard
              title="Total Contacts"
              value={stats.contacts}
              icon={<IconAddressBook size={18} />}
              color="green"
            />
            <StatCard
              title="Total Bookmarks"
              value={stats.bookmarks}
              icon={<IconBookmark size={18} />}
              color="orange"
            />
          </SimpleGrid>
        )}

        {/* Data Tables */}
        <Tabs defaultValue="profiles" variant="outline">
          <Tabs.List>
            <Tabs.Tab value="profiles" leftSection={<IconUsers size={16} />}>
              Profiles
            </Tabs.Tab>
            <Tabs.Tab value="contacts" leftSection={<IconAddressBook size={16} />}>
              Contacts
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="profiles" pt="md">
            <ProfilesTable />
          </Tabs.Panel>

          <Tabs.Panel value="contacts" pt="md">
            <ContactsTable />
          </Tabs.Panel>
        </Tabs>
      </Stack>
    </Container>
  );
}
