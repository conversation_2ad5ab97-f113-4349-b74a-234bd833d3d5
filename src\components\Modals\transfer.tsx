"use client";

import { useState, useEffect } from 'react';
import {
  Modal,
  TextInput,
  Button,
  Group,
  Text,
  Box,
  Loader,
  Select,
  Stack,
  Alert,
  Paper
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { IconMail, IconAlertCircle, IconCheck, IconX } from '@tabler/icons-react';
import { fetchData, updateData } from '../../lib/supabase';
import { MAX_CONTACTS_LIMIT } from '../../lib/config';

interface Contact {
  name: string;
  profile: string | null;
  image: string | null;
}

interface TransferModalProps {
  opened: boolean;
  onClose: () => void;
  contacts: Contact[];
  currentUserEmail: string;
  onTransferCompleted?: () => void;
  contactName?: string; // Optional specific contact name to transfer
}

export function TransferModal({
  opened,
  onClose,
  contacts,
  currentUserEmail,
  onTransferCompleted,
  contactName
}: TransferModalProps) {
  const [transferEmail, setTransferEmail] = useState('');
  const [selectedContact, setSelectedContact] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [isValidEmail, setIsValidEmail] = useState(false);
  const [emailChecked, setEmailChecked] = useState(false);
  const [receiverContactCount, setReceiverContactCount] = useState(0);
  const [receiverLimitReached, setReceiverLimitReached] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (opened) {
      setTransferEmail('');
      // If contactName is provided, use it directly; otherwise reset to empty
      setSelectedContact(contactName || '');
      setIsValidEmail(false);
      setEmailChecked(false);
      setReceiverContactCount(0);
      setReceiverLimitReached(false);
    }
  }, [opened, contactName]);

  // Validate email format
  const isEmailValid = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Check if user exists in profiles table and their contact limit
  const checkUserExists = async (email: string) => {
    if (!email || !isEmailValid(email)) {
      setIsValidEmail(false);
      setEmailChecked(false);
      setReceiverContactCount(0);
      setReceiverLimitReached(false);
      return;
    }

    setValidating(true);
    try {
      // First check if user exists in profiles table
      const { data: profileData, error: profileError } = await fetchData('profiles', {
        select: 'email',
        filter: [{ column: 'email', value: email }]
      });

      if (!profileError && profileData && Array.isArray(profileData) && profileData.length > 0) {
        // User exists, now check their contact count
        const { data: contactData, error: contactError } = await fetchData('contact', {
          select: 'name',
          filter: [{ column: 'profile_email', value: email }]
        });

        if (!contactError && contactData) {
          const contactCount = Array.isArray(contactData) ? contactData.length : 0;
          setReceiverContactCount(contactCount);
          setReceiverLimitReached(contactCount >= MAX_CONTACTS_LIMIT);
        } else {
          setReceiverContactCount(0);
          setReceiverLimitReached(false);
        }

        setIsValidEmail(true);
        setEmailChecked(true);
      } else {
        setIsValidEmail(false);
        setEmailChecked(true);
        setReceiverContactCount(0);
        setReceiverLimitReached(false);
      }
    } catch (error) {
      console.error('Error checking user:', error);
      setIsValidEmail(false);
      setEmailChecked(true);
      setReceiverContactCount(0);
      setReceiverLimitReached(false);
    } finally {
      setValidating(false);
    }
  };

  // Debounced email validation
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (transferEmail) {
        checkUserExists(transferEmail);
      } else {
        setIsValidEmail(false);
        setEmailChecked(false);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [transferEmail]);

  const handleTransfer = async () => {
    if (!selectedContact || !transferEmail || !isValidEmail) {
      notifications.show({
        title: 'Invalid Input',
        message: 'Please select a contact and enter a valid recipient email.',
        color: 'red',
      });
      return;
    }

    // Check if trying to transfer to same email
    if (transferEmail === currentUserEmail) {
      notifications.show({
        title: 'Invalid Transfer',
        message: 'You cannot transfer to your own email address.',
        color: 'orange',
      });
      return;
    }

    // Check if receiver has reached their contact limit
    if (receiverLimitReached) {
      notifications.show({
        title: 'Transfer Not Allowed',
        message: `The recipient has reached their maximum limit of ${MAX_CONTACTS_LIMIT} ODude Names. Transfer cannot be completed.`,
        color: 'red',
      });
      return;
    }

    setLoading(true);
    try {
      // Update the profile_email in contact table
      const { error } = await updateData(
        'contact',
        { profile_email: transferEmail },
        { column: 'name', value: selectedContact.toLowerCase() }
      );

      if (error) {
        throw error;
      }

      notifications.show({
        title: 'Transfer Successful',
        message: `${selectedContact} has been successfully transferred to ${transferEmail}`,
        color: 'green',
      });

      // Call completion callback
      if (onTransferCompleted) {
        onTransferCompleted();
      }

      // Close modal
      onClose();
    } catch (error) {
      console.error('Transfer error:', error);
      notifications.show({
        title: 'Transfer Failed',
        message: 'Failed to transfer the contact. Please try again.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Prepare contact options for select
  const contactOptions = contacts.map(contact => ({
    value: contact.name,
    label: `${contact.profile || contact.name} (${contact.name})`
  }));

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={contactName ? `Transfer ${contactName}` : "Transfer ODude Name"}
      centered
      size="md"
    >
      <Stack gap="md">
        <Text size="sm" c="dimmed">
          Transfer ownership of your ODude Name to another user. The recipient must have an account in our system.
        </Text>

        {/* Contact Selection - only show if no specific contactName provided */}
        {!contactName && (
          <Select
            label="Select Contact to Transfer"
            placeholder="Choose which contact to transfer"
            data={contactOptions}
            value={selectedContact}
            onChange={(value) => setSelectedContact(value || '')}
            required
            disabled={loading}
          />
        )}

        {/* Show selected contact info when contactName is provided */}
        {contactName && (
          <Box>
            <Text size="sm" fw={500} mb="xs">Contact to Transfer</Text>
            <Box p="sm" style={{ borderRadius: '4px' }}>
              <Text size="sm" fw={500}>
                {contacts.find(c => c.name.toLowerCase() === contactName.toLowerCase())?.profile || contactName}
              </Text>
              <Text size="xs" c="dimmed">{contactName}</Text>
            </Box>
          </Box>
        )}

        {/* Email Input */}
        <Box>
          <TextInput
            label="Recipient Email Address"
            placeholder="Enter recipient's email"
            value={transferEmail}
            onChange={(e) => setTransferEmail(e.target.value.toLowerCase().trim())}
            leftSection={<IconMail size={16} />}
            required
            disabled={loading}
            error={emailChecked && !isValidEmail ? 'User not found in our system' : undefined}
          />
          
          {validating && (
            <Group gap="xs" mt="xs">
              <Loader size="xs" />
              <Text size="xs" c="dimmed">Checking user...</Text>
            </Group>
          )}

          {emailChecked && isValidEmail && !receiverLimitReached && (
            <Group gap="xs" mt="xs">
              <IconCheck size={16} color="green" />
              <Text size="xs" c="green">
                User found and verified ({receiverContactCount}/{MAX_CONTACTS_LIMIT} ODude Names)
              </Text>
            </Group>
          )}

          {emailChecked && isValidEmail && receiverLimitReached && (
            <Group gap="xs" mt="xs">
              <IconX size={16} color="red" />
              <Text size="xs" c="red">
                User found but has reached maximum limit ({receiverContactCount}/{MAX_CONTACTS_LIMIT} ODude Names)
              </Text>
            </Group>
          )}
        </Box>

        {/* Warning Alert */}
        {selectedContact && transferEmail && isValidEmail && !receiverLimitReached && (
          <Alert icon={<IconAlertCircle size={16} />} color="yellow">
            <Text size="sm">
              <strong>Warning:</strong> This action cannot be undone. Once transferred,
              you will lose access to "{selectedContact}" and it will belong to {transferEmail}.
            </Text>
          </Alert>
        )}

        {/* Limit Reached Alert */}
        {selectedContact && transferEmail && isValidEmail && receiverLimitReached && (
          <Alert icon={<IconX size={16} />} color="red">
            <Text size="sm">
              <strong>Transfer Blocked:</strong> The recipient has reached their maximum limit of {MAX_CONTACTS_LIMIT} ODude Names.
              They must delete an existing name before receiving a transfer.
            </Text>
          </Alert>
        )}

        {/* Action Buttons */}
        <Group justify="flex-start" mt="md">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleTransfer}
            loading={loading}
            disabled={!selectedContact || !transferEmail || !isValidEmail || validating || receiverLimitReached}
            color="blue"
          >
            Transfer
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
