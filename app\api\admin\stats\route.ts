import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { ADMIN_EMAIL } from 'src/lib/config';
import { auth } from 'auth';

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const supabase = getSupabaseClient();

    // Get total profiles count
    const { count: profilesCount, error: profilesError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    if (profilesError) {
      console.error('Error fetching profiles count:', profilesError);
      return NextResponse.json({ error: 'Failed to fetch profiles count' }, { status: 500 });
    }

    // Get total contacts count
    const { count: contactsCount, error: contactsError } = await supabase
      .from('contact')
      .select('*', { count: 'exact', head: true });

    if (contactsError) {
      console.error('Error fetching contacts count:', contactsError);
      return NextResponse.json({ error: 'Failed to fetch contacts count' }, { status: 500 });
    }

    // Get total bookmarks count
    const { count: bookmarksCount, error: bookmarksError } = await supabase
      .from('bookmark')
      .select('*', { count: 'exact', head: true });

    if (bookmarksError) {
      console.error('Error fetching bookmarks count:', bookmarksError);
      return NextResponse.json({ error: 'Failed to fetch bookmarks count' }, { status: 500 });
    }

    return NextResponse.json({
      profiles: profilesCount || 0,
      contacts: contactsCount || 0,
      bookmarks: bookmarksCount || 0,
    });

  } catch (error) {
    console.error('Admin stats API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
