"use client"

import { useSession } from "next-auth/react"
import { UserInfo } from "../Card/user";
import {ActionsGrid } from "../Card/ActionsGrid";

import { Divider, Grid } from '@mantine/core';
import { fetchData } from '../../lib/supabase';
import { useEffect, useState } from "react";
import { getUserValue, setUserValue } from '../../lib/common';
import { MAX_CONTACTS_LIMIT } from '../../lib/config';
import { AdSenseBanner } from "../AdSense";


export const SignedIn = () => {
  const { data: session } = useSession();
  const [contacts, setContacts] = useState<any[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const fetchContacts = async () => {
    if (session?.user?.email) {
      const { data, error } = await fetchData('contact', {
        select: 'name, image, profile',
        filter: [{
          column: 'profile_email',
          value: session.user.email
        }]
      });

      if (!error && data) {
        setContacts(data as any[]);
      }
    }
  };

  const triggerRefresh = () => {
    fetchContacts();
    setRefreshTrigger(prev => prev + 1); // This will trigger ActionsGrid to refresh
  };

  useEffect(() => {
    fetchContacts();
  }, [session]);

  const saveUserDataToLocalStorage = async () => {
    if (session) {
      const { name: full_name, image: avatar_url } = session?.user || {};
      const { email } = session?.user || {};

      // Only handle localStorage operations here
      // Profile creation is now handled in NextAuth callback
      if (getUserValue(email,'full_name') == null) {
        console.log('Set local storage');
        setUserValue(email, 'full_name', full_name || '');
        setUserValue(email, 'avatar_url', avatar_url || '');
        window.dispatchEvent(new Event('userDataUpdated'));
      }
    }
  };

  useEffect(() => {
    saveUserDataToLocalStorage();
  }, [session]);

  return (
   <div>
          <ActionsGrid onContactCreated={fetchContacts} refreshTrigger={refreshTrigger}/>
            <Divider id="div-label" my="xs" label={`My ODude Names (${contacts.length}/${MAX_CONTACTS_LIMIT})`} labelPosition="center" w="100%" />
          <Grid>

        {contacts.map((contact) => (
                 <Grid.Col key={contact.name} span={6}><UserInfo name={contact.name} /></Grid.Col>
        ))}
  </Grid>
  <br/>
  
   <AdSenseBanner slot="4503599449" responsive={false} width={468} height={60} />
      </div>


  )
}


