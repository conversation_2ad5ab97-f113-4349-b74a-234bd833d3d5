import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { ADMIN_EMAIL } from 'src/lib/config';
import { auth } from 'auth';

export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session?.user?.email || session.user.email !== ADMIN_EMAIL) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    const supabase = getSupabaseClient();

    // Get profiles with contact count
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select(`
        id,
        created_at,
        updated_at,
        full_name,
        email,
        avatar_url
      `)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return NextResponse.json({ error: 'Failed to fetch profiles' }, { status: 500 });
    }

    // Get contact counts for each profile
    const profilesWithCounts = await Promise.all(
      (profiles || []).map(async (profile) => {
        const { count: contactCount } = await supabase
          .from('contact')
          .select('*', { count: 'exact', head: true })
          .eq('profile_email', profile.email);

        return {
          ...profile,
          contact_count: contactCount || 0,
        };
      })
    );

    // Get total count for pagination
    const { count: totalCount, error: countError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error fetching profiles count:', countError);
      return NextResponse.json({ error: 'Failed to fetch profiles count' }, { status: 500 });
    }

    return NextResponse.json({
      data: profilesWithCounts,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit),
      },
    });

  } catch (error) {
    console.error('Admin profiles API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
